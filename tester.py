import pygame
import random
import os
import sys
import math

# --- Pygame Initialization ---
pygame.init()
pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)

# --- Game Constants ---
SCREEN_WIDTH = 1000
SCREEN_HEIGHT = 700
FPS = 60
MENU_FPS = 30

# Set up the display
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Attack on Titan: Survive")

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (150, 150, 150)
DARK_RED = (139, 0, 0)
GOLD = (255, 215, 0)
LIGHT_BLUE = (173, 216, 230)  # For sky
DARK_GRAY = (50, 50, 50)  # For shadows

# Game World Dimensions
WORLD_WIDTH = 6000
WORLD_HEIGHT = 6000

# Third-Person View settings
THIRD_PERSON = True  # Toggle between third-person and top-down view
FOV_ANGLE = 90  # Field of view in degrees
VIEW_DISTANCE = 1500  # Maximum view distance (increased for better depth)
HORIZON_Y = SCREEN_HEIGHT // 2  # Horizon line position
SKY_COLOR = (135, 206, 235)  # Sky blue color

# 3D Projection settings
CAMERA_HEIGHT = 150  # Camera height above ground (increased for better view)
CAMERA_DISTANCE = 250  # Distance behind player (increased for better view)
CAMERA_ANGLE_OFFSET = math.pi  # Camera looks opposite to player direction
WALL_HEIGHT = 120  # Height of objects in 3D space
TITAN_HEIGHT_OFFSET = 30  # How much to hide titan legs

# Enhanced 3D settings
ENABLE_FOG = True  # Enable distance fog
FOG_COLOR = (200, 200, 255)  # Light blue-gray fog
FOG_START_DISTANCE = 800  # Distance at which fog starts
FOG_END_DISTANCE = 1400  # Distance at which fog is maximum

# Ground grid settings
GRID_SIZE = 100  # Size of each grid cell
GRID_ROWS = 20  # Number of grid rows to draw
GRID_COLS = 20  # Number of grid columns to draw
GRID_COLOR = (100, 100, 100)  # Grid line color
GROUND_COLOR = (80, 80, 80)  # Ground color

# Lighting settings
AMBIENT_LIGHT = 0.7  # Base ambient light level (0-1)
DIRECTIONAL_LIGHT = 0.3  # Directional light intensity (0-1)
LIGHT_DIRECTION = (0.5, 0.8, -0.2)  # Directional light vector (normalized)

# Camera pitch settings
MAX_PITCH = math.pi / 3  # Maximum look up angle (60 degrees)
MIN_PITCH = -math.pi / 6  # Maximum look down angle (-30 degrees)
PITCH_SENSITIVITY = 0.002  # Mouse sensitivity for vertical look

# --- Asset Paths ---
ASSET_DIR = ""

IMAGE_PATHS = {
    'loading_screen': os.path.join(ASSET_DIR, 'AOT_loadingscreen.png'),
    'player_sprite': os.path.join(ASSET_DIR, 'levi.png'),
    'mikasa_sprite': os.path.join(ASSET_DIR, 'mikasa.png'),
    'beast_titan_sprite': os.path.join(ASSET_DIR, 'attack-on-titan-beast-titan-1.png'),
    'titan_sprite': os.path.join(ASSET_DIR, 'titan.png'),
    'bg_buildings': os.path.join(ASSET_DIR, 'AOT Background buildings.png'),
    'midground': os.path.join(ASSET_DIR, 'midground.png'),
    'foreground': os.path.join(ASSET_DIR, 'foreground.png'),
    'shiganshina': os.path.join(ASSET_DIR, 'shiganshina.png'),
    'ground': os.path.join(ASSET_DIR, 'ground.png')
}

SOUND_PATHS = {
    'menu_music': os.path.join(ASSET_DIR, 'menu_ost.mp3'),
    'game_music_1': os.path.join(ASSET_DIR, 'game_ost(1).mp3'),
    'game_music_2': os.path.join(ASSET_DIR, 'game_ost(2).mp3'),
    'game_music_3': os.path.join(ASSET_DIR, 'game_ost(3).mp3'),
    'grapple_sound': os.path.join(ASSET_DIR, 'grapple.mp3'),
    'hit_sound': os.path.join(ASSET_DIR, 'hit.mp3'),
    'beast_hit_sound': os.path.join(ASSET_DIR, 'beast_hit.mp3'),
    'jump_sound': os.path.join(ASSET_DIR, 'jump.mp3')
}

# --- Asset Loading ---
def load_assets():
    """Loads all game images and sounds."""
    assets = {}
    
    # Load images
    for key, path in IMAGE_PATHS.items():
        try:
            image = pygame.image.load(path).convert_alpha()
            assets[key] = image
            print(f"Loaded image: {path}")
        except pygame.error as e:
            print(f"Error loading image {path}: {e}")
            # Load a placeholder if image fails to load
            placeholder_surface = pygame.Surface((100, 100))
            placeholder_surface.fill(GRAY)
            font = pygame.font.Font(None, 20)
            text_surf = font.render(key, True, BLACK)
            text_rect = text_surf.get_rect(center=(50, 50))
            placeholder_surface.blit(text_surf, text_rect)
            assets[key] = placeholder_surface

    # Load sounds
    for key, path in SOUND_PATHS.items():
        try:
            if 'music' in key:
                # Don't load music into memory, just store the path
                assets[key] = path
                print(f"Registered music: {path}")
            else:
                sound = pygame.mixer.Sound(path)
                assets[key] = sound
                print(f"Loaded sound: {path}")
        except pygame.error as e:
            print(f"Error loading sound {path}: {e}")
            assets[key] = None

    return assets

# --- Music Management ---
current_music = None
music_volume = 0.7

def play_music(music_path, loops=-1):
    """Play background music."""
    global current_music
    if music_path and current_music != music_path:
        try:
            pygame.mixer.music.load(music_path)
            pygame.mixer.music.set_volume(music_volume)
            pygame.mixer.music.play(loops)
            current_music = music_path
        except pygame.error as e:
            print(f"Error playing music {music_path}: {e}")

def stop_music():
    """Stop background music."""
    global current_music
    pygame.mixer.music.stop()
    current_music = None

# --- True Third-Person Camera System ---
def get_camera_position(player_x, player_y, player_angle):
    """Calculate the camera position for third-person view."""
    # Camera is positioned behind the player (opposite direction)
    camera_angle = player_angle + CAMERA_ANGLE_OFFSET
    camera_x = player_x + CAMERA_DISTANCE * math.cos(camera_angle)
    camera_y = player_y + CAMERA_DISTANCE * math.sin(camera_angle)
    return camera_x, camera_y

def world_to_screen_3d(world_x, world_y, camera_x, camera_y, camera_angle, camera_pitch=0):
    """Convert world coordinates to third-person screen coordinates with pitch support."""
    if not THIRD_PERSON:
        # Fallback to top-down view
        return world_x - camera_x + SCREEN_WIDTH//2, world_y - camera_y + SCREEN_HEIGHT//2, 1.0, 0

    # Translate to camera-relative coordinates
    rel_x = world_x - camera_x
    rel_y = world_y - camera_y

    # Rotate based on camera facing direction (same as player)
    cos_angle = math.cos(camera_angle)
    sin_angle = math.sin(camera_angle)

    # Transform to camera space (z is forward, x is right)
    cam_x = rel_x * cos_angle + rel_y * sin_angle
    cam_z = -rel_x * sin_angle + rel_y * cos_angle

    # Avoid division by zero and objects behind camera
    if cam_z <= 1:
        return None, None, 0, 0

    # Calculate distance for fog and culling
    distance = math.sqrt(rel_x**2 + rel_y**2)
    
    # Cull objects beyond view distance
    if distance > VIEW_DISTANCE:
        return None, None, 0, 0

    # Project to screen coordinates with perspective
    perspective_factor = 300  # Controls field of view
    screen_x = SCREEN_WIDTH // 2 + (cam_x * perspective_factor) / cam_z

    # Calculate Y position with height consideration and pitch
    # Objects on the ground appear lower, objects at camera height appear at horizon
    ground_height = 0  # Assume objects are on ground level
    height_offset = (CAMERA_HEIGHT - ground_height) * perspective_factor / cam_z

    # Apply pitch to the screen Y coordinate
    base_screen_y = HORIZON_Y + height_offset
    pitch_offset = camera_pitch * perspective_factor / cam_z * 100  # Scale pitch effect
    screen_y = base_screen_y - pitch_offset

    # Calculate scale based on distance - more dramatic scaling for buildings
    scale = perspective_factor / cam_z
    scale = max(0.05, min(4.0, scale))  # Allow bigger scaling range for buildings

    # Calculate fog factor (0 = no fog, 1 = full fog)
    fog_factor = 0
    if ENABLE_FOG and distance > FOG_START_DISTANCE:
        fog_factor = min(1.0, (distance - FOG_START_DISTANCE) / (FOG_END_DISTANCE - FOG_START_DISTANCE))

    return screen_x, screen_y, scale, fog_factor

def draw_third_person_background(player_angle, pitch=0):
    """Draw the third-person background with dynamic sky, horizon, and ground."""
    if not THIRD_PERSON:
        return

    # Create a gradient sky
    sky_rect = pygame.Rect(0, 0, SCREEN_WIDTH, HORIZON_Y)
    ground_rect = pygame.Rect(0, HORIZON_Y, SCREEN_WIDTH, SCREEN_HEIGHT - HORIZON_Y)
    
    # Adjust horizon based on pitch
    pitch_offset = pitch * 200  # Scale pitch effect on horizon
    adjusted_horizon = HORIZON_Y - pitch_offset
    
    # Clamp horizon to screen bounds
    adjusted_horizon = max(50, min(SCREEN_HEIGHT - 50, adjusted_horizon))
    
    # Update rects based on adjusted horizon
    sky_rect.height = adjusted_horizon
    ground_rect.y = adjusted_horizon
    ground_rect.height = SCREEN_HEIGHT - adjusted_horizon
    
    # Draw sky gradient (lighter at horizon, darker at top)
    sky_surface = pygame.Surface((SCREEN_WIDTH, sky_rect.height), pygame.SRCALPHA)
    for y in range(sky_rect.height):
        # Calculate gradient color (darker blue at top, lighter at horizon)
        gradient_factor = y / sky_rect.height
        r = int(SKY_COLOR[0] * (0.7 + 0.3 * gradient_factor))
        g = int(SKY_COLOR[1] * (0.7 + 0.3 * gradient_factor))
        b = int(SKY_COLOR[2] * (0.7 + 0.3 * gradient_factor))
        pygame.draw.line(sky_surface, (r, g, b), (0, y), (SCREEN_WIDTH, y))
    
    screen.blit(sky_surface, (0, 0))
    
    # Draw shiganshina as a distant city skyline
    shiganshina_image = assets.get('shiganshina')
    if shiganshina_image:
        # Scale shiganshina to fit horizon
        shiganshina_height = int(SCREEN_HEIGHT * 0.3)  # Take up 30% of screen height
        shiganshina_y = adjusted_horizon - shiganshina_height  # Position at horizon
        
        # Apply parallax effect based on player angle
        parallax_offset = int(player_angle * SCREEN_WIDTH / (2 * math.pi)) % SCREEN_WIDTH
        
        # Scale shiganshina
        shiganshina_scaled = pygame.transform.scale(
            shiganshina_image, 
            (SCREEN_WIDTH * 2, shiganshina_height)
        )
        
        # Create a surface with alpha for distance fog effect
        shiganshina_surface = pygame.Surface((SCREEN_WIDTH, shiganshina_height), pygame.SRCALPHA)
        shiganshina_surface.blit(shiganshina_scaled, (-parallax_offset, 0))
        shiganshina_surface.blit(shiganshina_scaled, (SCREEN_WIDTH - parallax_offset, 0))
        
        # Apply fog to the skyline
        fog_alpha = 150  # Semi-transparent fog
        fog_surface = pygame.Surface((SCREEN_WIDTH, shiganshina_height), pygame.SRCALPHA)
        fog_surface.fill((*FOG_COLOR, fog_alpha))
        shiganshina_surface.blit(fog_surface, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
        
        # Draw the skyline
        if shiganshina_y > 0:  # Only draw if visible
            screen.blit(shiganshina_surface, (0, shiganshina_y))
    
    # Draw ground with perspective grid
    draw_ground_grid(adjusted_horizon, player_angle, pitch)
def draw_ground_grid(horizon_y, player_angle, pitch):
    """Draw a perspective grid on the ground for better depth perception."""
    # Draw ground base color
    ground_rect = pygame.Rect(0, horizon_y, SCREEN_WIDTH, SCREEN_HEIGHT - horizon_y)
    pygame.draw.rect(screen, GROUND_COLOR, ground_rect)
    
    # Calculate vanishing point
    vanishing_x = SCREEN_WIDTH // 2
    vanishing_y = horizon_y
    
    # Calculate grid based on player angle and pitch
    angle_offset = player_angle % (math.pi / 2)  # Get angle within 90 degree segment
    
    # Draw horizontal grid lines (parallel to horizon)
    for i in range(1, GRID_ROWS + 1):
        # Calculate y position with perspective
        # Lines get closer together as they approach the horizon
        distance_factor = i / GRID_ROWS
        y_pos = int(horizon_y + (SCREEN_HEIGHT - horizon_y) * distance_factor**2)
        
        # Apply fog effect to lines
        fog_factor = distance_factor
        line_color = blend_colors(GRID_COLOR, FOG_COLOR, fog_factor)
        line_alpha = int(255 * (1 - fog_factor * 0.7))
        
        # Draw the line with alpha
        line_surface = pygame.Surface((SCREEN_WIDTH, 1), pygame.SRCALPHA)
        line_surface.fill((*line_color, line_alpha))
        screen.blit(line_surface, (0, y_pos))
    
    # Draw vertical grid lines (converging at vanishing point)
    num_lines = GRID_COLS * 2  # More lines for better effect
    for i in range(num_lines):
        # Calculate angle for this line
        angle = (i / num_lines) * math.pi - math.pi/2 + player_angle
        
        # Calculate line endpoints
        line_length = SCREEN_HEIGHT * 2  # Make sure lines are long enough
        end_x = vanishing_x + math.cos(angle) * line_length
        end_y = vanishing_y + math.sin(angle) * line_length
        
        # Apply fog effect
        fog_factor = 0.5  # Fixed fog for vertical lines
        line_color = blend_colors(GRID_COLOR, FOG_COLOR, fog_factor)
        
        # Draw the line
        pygame.draw.line(screen, line_color, (vanishing_x, vanishing_y), (end_x, end_y), 1)

def blend_colors(color1, color2, blend_factor):
    """Blend two colors based on blend factor (0-1)."""
    r = int(color1[0] * (1 - blend_factor) + color2[0] * blend_factor)
    g = int(color1[1] * (1 - blend_factor) + color2[1] * blend_factor)
    b = int(color1[2] * (1 - blend_factor) + color2[2] * blend_factor)
    return (r, g, b)

def apply_fog(surface, fog_factor):
    """Apply fog effect to a surface based on distance."""
    if fog_factor <= 0:
        return surface
    
    # Create a copy of the surface
    fogged_surface = surface.copy()
    
    # Create a fog overlay
    fog_overlay = pygame.Surface(surface.get_size(), pygame.SRCALPHA)
    fog_alpha = int(255 * fog_factor)
    fog_overlay.fill((*FOG_COLOR, fog_alpha))
    
    # Apply fog overlay
    fogged_surface.blit(fog_overlay, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
    
    return fogged_surface

def apply_lighting(surface, normal_vector=(0, 0, 1)):
    """Apply simple lighting to a surface based on normal vector."""
    # Skip lighting for small surfaces
    if surface.get_width() < 10 or surface.get_height() < 10:
        return surface
    
    # Create a copy of the surface
    lit_surface = surface.copy()
    
    # Calculate lighting factor based on normal vector and light direction
    # Dot product of normal and light direction
    light_factor = (
        normal_vector[0] * LIGHT_DIRECTION[0] +
        normal_vector[1] * LIGHT_DIRECTION[1] +
        normal_vector[2] * LIGHT_DIRECTION[2]
    )
    
    # Clamp light factor and scale to ambient + directional
    light_factor = max(0, light_factor)
    light_factor = AMBIENT_LIGHT + DIRECTIONAL_LIGHT * light_factor
    light_factor = min(1.0, light_factor)
    
    # Create a lighting overlay
    light_overlay = pygame.Surface(surface.get_size(), pygame.SRCALPHA)
    light_alpha = int(255 * light_factor)
    light_overlay.fill((255, 255, 255, light_alpha))
    
    # Apply lighting overlay
    lit_surface.blit(light_overlay, (0, 0), special_flags=pygame.BLEND_RGBA_MULT)
    
    return lit_surface
# --- Building System ---
class Building:
    def __init__(self, x, y, sprite_type='bg_buildings'):
        self.x = x
        self.y = y
        self.sprite_type = sprite_type

        # Get building sprite and determine size
        self.sprite = assets.get(sprite_type)
        if self.sprite:
            # Use a portion of the building sprite sheet - make buildings bigger
            self.sprite_width = self.sprite.get_width() // 3  # Larger sections (3x2 instead of 4x3)
            self.sprite_height = self.sprite.get_height() // 2
            self.width = self.sprite_width * 1.5  # Make buildings 50% bigger
            self.height = self.sprite_height * 1.5
        else:
            # Fallback dimensions - also bigger
            self.width = 180
            self.height = 220

        self.rect = pygame.Rect(x, y, self.width, self.height)

        # Random section of the sprite sheet to use (3x2 grid)
        self.sprite_section_x = random.randint(0, 2) if self.sprite else 0
        self.sprite_section_y = random.randint(0, 1) if self.sprite else 0

    def get_sprite(self, scale=1.0):
        """Get the building sprite at the specified scale."""
        if not self.sprite:
            # Return a simple colored rectangle if no sprite
            surface = pygame.Surface((int(self.width * scale), int(self.height * scale)))
            surface.fill((80, 80, 80))
            return surface

        # Extract the specific section of the building sprite
        section_rect = pygame.Rect(
            self.sprite_section_x * self.sprite_width,
            self.sprite_section_y * self.sprite_height,
            self.sprite_width,
            self.sprite_height
        )

        # Create surface for this building section
        building_surface = pygame.Surface((self.sprite_width, self.sprite_height), pygame.SRCALPHA)

        # Check bounds and extract sprite section
        if (section_rect.right <= self.sprite.get_width() and
            section_rect.bottom <= self.sprite.get_height()):
            building_surface.blit(self.sprite, (0, 0), section_rect)
        else:
            # Fallback to simple rectangle
            building_surface.fill((80, 80, 80))

        # Scale the building sprite (apply both base scaling and distance scaling)
        final_width = int(self.width * scale)
        final_height = int(self.height * scale)
        building_surface = pygame.transform.scale(building_surface, (final_width, final_height))

        return building_surface
# 3D Renderable class for z-sorting
class Renderable3D:
    def __init__(self, world_x, world_y, world_z, sprite, width, height):
        self.world_x = world_x
        self.world_y = world_y
        self.world_z = world_z  # Height above ground
        self.sprite = sprite
        self.width = width
        self.height = height
        self.distance = 0  # Will be calculated during rendering
        self.screen_x = 0
        self.screen_y = 0
        self.scale = 1.0
        self.fog_factor = 0
        
    def update_projection(self, camera_x, camera_y, camera_angle, camera_pitch):
        """Update screen coordinates based on camera position."""
        self.screen_x, self.screen_y, self.scale, self.fog_factor = world_to_screen_3d(
            self.world_x, self.world_y, camera_x, camera_y, camera_angle, camera_pitch
        )
        
        # Calculate distance from camera for z-sorting
        dx = self.world_x - camera_x
        dy = self.world_y - camera_y
        self.distance = math.sqrt(dx*dx + dy*dy)
        
        return self.screen_x is not None
        
    def render(self, screen):
        """Render the object to the screen with all effects applied."""
        if self.screen_x is None:
            return False
            
        # Scale sprite
        scaled_width = int(self.width * self.scale)
        scaled_height = int(self.height * self.scale)
        
        # Skip if too small
        if scaled_width < 2 or scaled_height < 2:
            return False
            
        # Scale sprite
        scaled_sprite = pygame.transform.scale(self.sprite, (scaled_width, scaled_height))
        
        # Apply fog effect
        if self.fog_factor > 0:
            scaled_sprite = apply_fog(scaled_sprite, self.fog_factor)
        
        # Calculate draw position
        draw_x = int(self.screen_x - scaled_width // 2)
        draw_y = int(self.screen_y - scaled_height)
        
        # Only draw if on screen
        if (draw_x + scaled_width >= 0 and draw_x <= SCREEN_WIDTH and
            draw_y + scaled_height >= 0 and draw_y <= SCREEN_HEIGHT):
            screen.blit(scaled_sprite, (draw_x, draw_y))
            return True
            
        return False

def generate_buildings():
    """Generate buildings in organized rows with alleys between them."""
    buildings = []

    # Available building sprite types (removed shiganshina - now used as background)
    building_sprites = ['bg_buildings', 'midground', 'foreground']

    # Building and alley dimensions
    building_width = 200  # Average building width
    building_height = 250  # Average building height
    alley_width = 150  # Width of alleys between building rows
    buildings_per_row = 8  # Number of buildings per row
    row_spacing = building_height + alley_width  # Space between rows
    building_spacing = building_width + 50  # Space between buildings in same row

    # Calculate grid layout
    total_rows = 6
    start_x = 500  # Start away from world center
    start_y = 500

    # Player spawn area (center of world) - avoid placing buildings here
    player_spawn_x = WORLD_WIDTH // 2
    player_spawn_y = WORLD_HEIGHT // 2
    spawn_clearance = 400  # Radius around player spawn to keep clear

    for row in range(total_rows):
        for col in range(buildings_per_row):
            # Calculate building position
            x = start_x + col * building_spacing
            y = start_y + row * row_spacing

            # Skip if too close to player spawn area
            distance_to_spawn = math.sqrt((x - player_spawn_x)**2 + (y - player_spawn_y)**2)
            if distance_to_spawn < spawn_clearance:
                continue

            # Ensure building stays within world bounds
            if x + building_width < WORLD_WIDTH - 200 and y + building_height < WORLD_HEIGHT - 200:
                # Random building sprite type
                sprite_type = random.choice(building_sprites)
                buildings.append(Building(x, y, sprite_type))

    # Add some buildings on the other side of the spawn area for variety
    for row in range(total_rows):
        for col in range(buildings_per_row):
            # Calculate building position on opposite side
            x = player_spawn_x + spawn_clearance + 200 + col * building_spacing
            y = start_y + row * row_spacing

            # Ensure building stays within world bounds
            if x + building_width < WORLD_WIDTH - 200 and y + building_height < WORLD_HEIGHT - 200:
                # Random building sprite type
                sprite_type = random.choice(building_sprites)
                buildings.append(Building(x, y, sprite_type))

    return buildings

def calculate_perspective_scale(y_position):
    """Calculate the scale factor based on Y position for perspective effect (top-down mode)."""
    if THIRD_PERSON:
        return 1.0  # No perspective scaling in third-person mode

    # Top-down perspective scaling (legacy)
    PERSPECTIVE_CENTER_Y = WORLD_HEIGHT / 2
    PERSPECTIVE_SCALE_FACTOR = 0.0002
    MIN_SCALE = 0.3
    MAX_SCALE = 2.0

    distance_from_center = y_position - PERSPECTIVE_CENTER_Y
    scale = 1.0 + (distance_from_center * PERSPECTIVE_SCALE_FACTOR)
    scale = max(MIN_SCALE, min(MAX_SCALE, scale))
    return scale

def get_perspective_size(original_width, original_height, y_position):
    """Get the scaled size based on perspective."""
    scale = calculate_perspective_scale(y_position)
    return int(original_width * scale), int(original_height * scale)

# --- Game State ---
game_state = 'MENU' # MENU, LOADING, PLAYING, PAUSED, GAME_OVER
assets = {} # Will store loaded images and sounds
camera_x, camera_y = 0, 0 # Camera position
selected_character = 'levi' # 'levi' or 'mikasa'
game_running = True  # Flag to control clean shutdown

# Game statistics
total_kills = 0
total_score = 0
wave_number = 1
titans_killed_this_wave = 0
titans_needed_for_next_wave = 5

# --- Sprite Sheet Data ---
PLAYER_SPRITE_DATA = {
    'frame_width': 61,
    'frame_height': 58,
    'scale': 2.0,
    'animations': {
        'idle': [(0, 0), (0, 1), (0, 2)],  # Top row frames 0-2
        'walk_right': [(1, 0)],  # Bottom row frame 0
        'walk_left': [(1, 0)],   # Same frame, will be flipped
        'attack': [(1, 1)]       # Bottom row frame 1
    }
}

TITAN_SPRITE_DATA = {
    'frame_width': 96,
    'frame_height': 96,
    'scale': 3.0,  # Increased from 2.0 to 3.0 to make titans bigger
    'animations': {
        'idle': [(0, 0)],
        'walk': [(0, 0)],
        'attack': [(0, 0)]
    }
}

# --- Particle System ---
class Particle:
    def __init__(self, x, y, z, color, velocity_x, velocity_y, velocity_z, lifetime, size=4):
        self.x = x
        self.y = y
        self.z = z  # Height above ground
        self.color = color
        self.velocity_x = velocity_x
        self.velocity_y = velocity_y
        self.velocity_z = velocity_z
        self.lifetime = lifetime
        self.max_lifetime = lifetime
        self.size = size
        self.distance = 0  # For z-sorting
        
    def update(self, dt):
        self.x += self.velocity_x * dt / 1000
        self.y += self.velocity_y * dt / 1000
        self.z += self.velocity_z * dt / 1000
        
        # Apply gravity to z velocity
        self.velocity_z -= 9.8 * dt / 1000  # Simple gravity
        
        # Bounce off ground
        if self.z < 0:
            self.z = 0
            self.velocity_z = -self.velocity_z * 0.5  # Dampen bounce
        
        self.lifetime -= dt
        return self.lifetime > 0
        
    def draw(self, screen, camera_x, camera_y, camera_angle, camera_pitch):
        # Project particle to screen space
        screen_x, screen_y, scale, fog_factor = world_to_screen_3d(
            self.x, self.y, camera_x, camera_y, camera_angle, camera_pitch
        )
        
        if screen_x is None:
            return
            
        # Calculate distance for z-sorting
        dx = self.x - camera_x
        dy = self.y - camera_y
        self.distance = math.sqrt(dx*dx + dy*dy)
        
        # Calculate alpha based on lifetime and fog
        lifetime_alpha = int(255 * (self.lifetime / self.max_lifetime))
        fog_alpha = int(255 * (1 - fog_factor))
        alpha = min(lifetime_alpha, fog_alpha)
        
        # Skip if fully transparent
        if alpha <= 10:
            return
            
        color_with_alpha = (*self.color[:3], alpha)
        
        # Scale particle size based on distance
        particle_size = int(self.size * scale)
        if particle_size < 1:
            particle_size = 1
        
        # Create a surface for the particle with alpha
        particle_surface = pygame.Surface((particle_size, particle_size), pygame.SRCALPHA)
        particle_surface.fill(color_with_alpha)
        
        # Add a glow effect for more visual impact
        if particle_size > 2:
            pygame.draw.circle(
                particle_surface, 
                (*self.color[:3], alpha // 2), 
                (particle_size // 2, particle_size // 2), 
                particle_size // 2
            )
        
        screen.blit(particle_surface, (int(screen_x - particle_size // 2), int(screen_y - particle_size // 2)))

particles = []

def create_hit_particles(x, y, color=RED):
    """Create particles when a titan is hit."""
    for _ in range(15):  # More particles for better effect
        velocity_x = random.uniform(-150, 150)
        velocity_y = random.uniform(-150, 150)
        velocity_z = random.uniform(50, 150)  # Upward velocity for 3D effect
        lifetime = random.uniform(400, 800)
        size = random.randint(3, 8)
        particles.append(Particle(x, y, 30, color, velocity_x, velocity_y, velocity_z, lifetime, size))

def create_grapple_particles(x, y):
    """Create particles for grappling hook effect."""
    for _ in range(8):  # More particles
        velocity_x = random.uniform(-80, 80)
        velocity_y = random.uniform(-80, 80)
        velocity_z = random.uniform(10, 50)
        lifetime = random.uniform(200, 500)
        particles.append(Particle(x, y, 20, BLUE, velocity_x, velocity_y, velocity_z, lifetime, 5))

def update_particles(dt):
    """Update all particles and remove dead ones."""
    global particles
    particles = [p for p in particles if p.update(dt)]

def draw_particles_3d(screen, camera_x, camera_y, camera_angle, camera_pitch):
    """Draw all particles with 3D projection."""
    # Sort particles by distance for proper z-ordering
    particles_to_draw = []
    
    for particle in particles:
        # Store distance for sorting
        dx = particle.x - camera_x
        dy = particle.y - camera_y
        particle.distance = math.sqrt(dx*dx + dy*dy)
        particles_to_draw.append(particle)
    
    # Sort particles by distance (furthest first)
    particles_to_draw.sort(key=lambda p: p.distance, reverse=True)
    
    # Draw particles in sorted order
    for particle in particles_to_draw:
        particle.draw(screen, camera_x, camera_y, camera_angle, camera_pitch)
def draw_grapple_line_3d(screen, player, camera_x, camera_y, camera_angle, camera_pitch):
    """Draw grappling hook line in 3D space."""
    if not player.is_grappling:
        return
        
    # Project player position to screen
    player_screen_x, player_screen_y, player_scale, _ = world_to_screen_3d(
        player.x + player.width/2, 
        player.y + player.height/2,
        camera_x, camera_y, camera_angle, camera_pitch
    )
    
    if player_screen_x is None:
        return
        
    # Project grapple target to screen
    target_screen_x, target_screen_y, target_scale, fog_factor = world_to_screen_3d(
        player.grapple_target_x,
        player.grapple_target_y,
        camera_x, camera_y, camera_angle, camera_pitch
    )
    
    if target_screen_x is None:
        return
        
    # Draw grapple line with thickness based on distance
    line_thickness = max(1, int(3 * min(player_scale, target_scale)))
    
    # Calculate line color with fog effect
    line_color = BLUE
    if fog_factor > 0:
        line_color = blend_colors(BLUE, FOG_COLOR, fog_factor)
    
    # Draw the line
    pygame.draw.line(
        screen, 
        line_color, 
        (int(player_screen_x), int(player_screen_y - player.height * player_scale / 2)), 
        (int(target_screen_x), int(target_screen_y)), 
        line_thickness
    )
    
    # Draw grapple target indicator
    target_radius = max(3, int(8 * target_scale))
    target_thickness = max(1, int(2 * target_scale))
    
    # Apply fog to target indicator color
    target_color = blend_colors(YELLOW, FOG_COLOR, fog_factor)
    
    pygame.draw.circle(
        screen, 
        target_color, 
        (int(target_screen_x), int(target_screen_y)), 
        target_radius, 
        target_thickness
    )
def draw_enemy_health_bar(screen, enemy, camera_x, camera_y, perspective_scale=1.0, screen_x=None, screen_y=None):
    """Draw health bar above enemy with perspective scaling."""
    # Health bar dimensions scaled by perspective
    base_bar_width = 60
    base_bar_height = 8
    bar_width = int(base_bar_width * perspective_scale)
    bar_height = int(base_bar_height * perspective_scale)

    if screen_x is not None and screen_y is not None:
        # First-person mode: use provided screen coordinates
        bar_x = screen_x - bar_width // 2
        bar_y = screen_y
    else:
        # Top-down mode: calculate from world coordinates
        enemy_visual_width = int(enemy.width * perspective_scale)
        bar_x = enemy.x + (enemy_visual_width - bar_width) // 2 - camera_x
        bar_y = enemy.y - int(15 * perspective_scale) - camera_y

    # Only draw if health bar is on screen
    if (bar_x + bar_width >= 0 and bar_x <= SCREEN_WIDTH and
        bar_y + bar_height >= 0 and bar_y <= SCREEN_HEIGHT):

        # Calculate health percentage
        max_health = getattr(enemy, 'max_health', 100)
        health_percentage = max(0, enemy.health / max_health)

        # Background (red)
        pygame.draw.rect(screen, DARK_RED, (bar_x, bar_y, bar_width, bar_height))

        # Health fill (green to red gradient based on health)
        if health_percentage > 0.6:
            health_color = GREEN
        elif health_percentage > 0.3:
            health_color = YELLOW
        else:
            health_color = RED

        health_fill_width = int(bar_width * health_percentage)
        if health_fill_width > 0:
            pygame.draw.rect(screen, health_color, (bar_x, bar_y, health_fill_width, bar_height))

        # Border (make border thicker for larger health bars)
        border_thickness = max(1, int(perspective_scale))
        pygame.draw.rect(screen, WHITE, (bar_x, bar_y, bar_width, bar_height), border_thickness)
# --- Z-Sorting Rendering System ---
def render_scene_3d(screen, player, buildings, titans, camera_x, camera_y):
    """Render the entire 3D scene with proper z-sorting."""
    # Draw background and ground
    draw_third_person_background(player.angle, player.pitch)
    
    # Create a list of all 3D objects to render
    renderables = []
    
    # Add buildings to renderables
    for building in buildings:
        # Skip if too far from camera
        dx = building.x + building.width/2 - camera_x
        dy = building.y + building.height/2 - camera_y
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance > VIEW_DISTANCE:
            continue
            
        # Get building sprite
        building_sprite = building.get_sprite()
        
        # Create renderable
        renderable = Renderable3D(
            building.x + building.width/2,
            building.y + building.height/2,
            0,  # Buildings are at ground level
            building_sprite,
            building.width,
            building.height
        )
        
        # Update projection
        if renderable.update_projection(camera_x, camera_y, player.angle, player.pitch):
            renderables.append(renderable)
    
    # Add titans to renderables
    for titan in titans:
        # Skip if too far from camera
        dx = titan.x + titan.width/2 - camera_x
        dy = titan.y + titan.height/2 - camera_y
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance > VIEW_DISTANCE:
            continue
            
        # Get titan sprite
        titan_sprite = titan.get_frame()
        
        # Flip sprite if facing left
        if hasattr(titan, 'facing_right') and not titan.facing_right:
            titan_sprite = pygame.transform.flip(titan_sprite, True, False)
        
        # Create renderable
        renderable = Renderable3D(
            titan.x + titan.width/2,
            titan.y + titan.height/2,
            30,  # Titans are slightly above ground
            titan_sprite,
            titan.width,
            titan.height
        )
        
        # Update projection
        if renderable.update_projection(camera_x, camera_y, player.angle, player.pitch):
            renderables.append(renderable)
            
            # Add titan health bar as separate renderable
            if titan.health > 0:
                # Create health bar surface
                bar_width = 60
                bar_height = 8
                health_bar = pygame.Surface((bar_width, bar_height), pygame.SRCALPHA)
                
                # Background (red)
                pygame.draw.rect(health_bar, DARK_RED, (0, 0, bar_width, bar_height))
                
                # Health fill
                health_percentage = max(0, titan.health / titan.max_health)
                if health_percentage > 0.6:
                    health_color = GREEN
                elif health_percentage > 0.3:
                    health_color = YELLOW
                else:
                    health_color = RED
                    
                health_fill_width = int(bar_width * health_percentage)
                if health_fill_width > 0:
                    pygame.draw.rect(health_bar, health_color, (0, 0, health_fill_width, bar_height))
                    
                # Border
                pygame.draw.rect(health_bar, WHITE, (0, 0, bar_width, bar_height), 1)
                
                # Create health bar renderable (positioned above titan)
                health_renderable = Renderable3D(
                    titan.x + titan.width/2,
                    titan.y + titan.height/2,
                    titan.height + 20,  # Position above titan
                    health_bar,
                    bar_width,
                    bar_height
                )
                
                # Update projection
                if health_renderable.update_projection(camera_x, camera_y, player.angle, player.pitch):
                    renderables.append(health_renderable)
    
    # Sort renderables by distance (furthest first for proper overlapping)
    renderables.sort(key=lambda r: r.distance, reverse=True)
    
    # Render all objects in sorted order
    for renderable in renderables:
        renderable.render(screen)
    
    # Draw player (always on top in third-person view)
    if player.is_attacking or player.is_grappling:
        # Add glow effect when attacking
        glow_surface = pygame.Surface((player.width + 16, player.height + 16), pygame.SRCALPHA)
        glow_color = (*GOLD[:3], 100)
        pygame.draw.rect(glow_surface, glow_color, (0, 0, player.width + 16, player.height + 16), border_radius=5)
        
        # Position glow at bottom center of screen
        glow_x = SCREEN_WIDTH // 2 - (player.width + 16) // 2
        glow_y = SCREEN_HEIGHT - (player.height + 16) - 20
        screen.blit(glow_surface, (glow_x, glow_y))
    
    # Draw player sprite at bottom center of screen
    player_sprite = player.get_frame()
    if hasattr(player, 'facing_right') and not player.facing_right:
        player_sprite = pygame.transform.flip(player_sprite, True, False)
        
    # Apply visual effects
    draw_player = True
    if player.invulnerable and (player.invulnerable_timer // 100) % 2:
        draw_player = False
        
    if draw_player:
        player_x = SCREEN_WIDTH // 2 - player.width // 2
        player_y = SCREEN_HEIGHT - player.height - 20
        screen.blit(player_sprite, (player_x, player_y))
    
    # Draw grapple line
    draw_grapple_line_3d(screen, player, camera_x, camera_y, player.angle, player.pitch)
    
    # Draw particles
    draw_particles_3d(screen, camera_x, camera_y, player.angle, player.pitch)
    
    # Draw crosshair
    center_x, center_y = SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2
    crosshair_size = 10
    crosshair_thickness = 2
    
    # Horizontal line
    pygame.draw.line(screen, WHITE,
                   (center_x - crosshair_size, center_y),
                   (center_x + crosshair_size, center_y), crosshair_thickness)
    # Vertical line
    pygame.draw.line(screen, WHITE,
                   (center_x, center_y - crosshair_size),
                   (center_x, center_y + crosshair_size), crosshair_thickness)
    
    # Show grappling status
    if player.is_grappling:
        pygame.draw.circle(screen, BLUE, (center_x, center_y), 15, 3)

# --- Game Entities ---
class Entity(pygame.sprite.Sprite):
    """Base class for player and enemies."""
    def __init__(self, x, y, width, height, sprite_sheet_img, sprite_data):
        super().__init__()
        self.x = x
        self.y = y
        self.original_width = width
        self.original_height = height
        self.sprite_sheet = sprite_sheet_img
        self.sprite_data = sprite_data
        self.current_animation = 'idle'
        self.frame_index = 0
        self.animation_timer = 0
        self.frame_delay = 100

        # Scale based on sprite_data
        self.width = int(self.original_width * self.sprite_data['scale'])
        self.height = int(self.original_height * self.sprite_data['scale'])

        # Create a surface for the current frame
        self.image = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
        self.rect = self.image.get_rect(topleft=(self.x, self.y))

        # Initialize with first frame
        self.image = self.get_frame()

    def get_frame(self, perspective_scale=None):
        """Extracts and scales the current frame from the sprite sheet."""
        try:
            # Ensure we have valid sprite data
            if not hasattr(self, 'sprite_data') or not self.sprite_data:
                return pygame.Surface((self.width, self.height), pygame.SRCALPHA)

            # Ensure we have a valid animation
            if (self.current_animation not in self.sprite_data['animations'] or
                not self.sprite_data['animations'][self.current_animation]):
                self.current_animation = 'idle'
                self.frame_index = 0

            # Fallback to idle if current animation doesn't exist
            if (self.current_animation not in self.sprite_data['animations'] or
                not self.sprite_data['animations'][self.current_animation]):
                return pygame.Surface((self.width, self.height), pygame.SRCALPHA)

            frames = self.sprite_data['animations'][self.current_animation]

            # Safety check for empty frames list
            if not frames or len(frames) == 0:
                return pygame.Surface((self.width, self.height), pygame.SRCALPHA)

            # Ensure frame_index is always within valid bounds
            if self.frame_index >= len(frames) or self.frame_index < 0:
                self.frame_index = 0

            # Get the frame coordinates
            if self.frame_index < len(frames):
                row, col = frames[self.frame_index]
            else:
                row, col = frames[0]
                self.frame_index = 0

            # Ensure sprite sheet exists
            if not self.sprite_sheet:
                return pygame.Surface((self.width, self.height), pygame.SRCALPHA)

            frame_rect = pygame.Rect(
                col * self.sprite_data['frame_width'],
                row * self.sprite_data['frame_height'],
                self.sprite_data['frame_width'],
                self.sprite_data['frame_height']
            )

            # Create a new surface to draw the frame onto
            frame_surface = pygame.Surface((self.sprite_data['frame_width'], self.sprite_data['frame_height']), pygame.SRCALPHA)

            # Check if the sprite sheet is large enough for this frame
            if (frame_rect.right <= self.sprite_sheet.get_width() and
                frame_rect.bottom <= self.sprite_sheet.get_height()):
                frame_surface.blit(self.sprite_sheet, (0, 0), frame_rect)
            else:
                # If frame is out of bounds, use the first frame (0,0)
                fallback_rect = pygame.Rect(0, 0, self.sprite_data['frame_width'], self.sprite_data['frame_height'])
                if (fallback_rect.right <= self.sprite_sheet.get_width() and
                    fallback_rect.bottom <= self.sprite_sheet.get_height()):
                    frame_surface.blit(self.sprite_sheet, (0, 0), fallback_rect)

            # Apply perspective scaling if provided
            if perspective_scale is not None:
                scaled_width = int(self.width * perspective_scale)
                scaled_height = int(self.height * perspective_scale)
                return pygame.transform.scale(frame_surface, (scaled_width, scaled_height))
            else:
                return pygame.transform.scale(frame_surface, (self.width, self.height))

        except (IndexError, TypeError, AttributeError, KeyError):
            # If any error occurs, return a blank surface
            return pygame.Surface((self.width, self.height), pygame.SRCALPHA)

    def update_animation(self, dt):
        """Updates the animation frame based on delta time."""
        try:
            # Ensure we have valid sprite data
            if not hasattr(self, 'sprite_data') or not self.sprite_data:
                return

            # Ensure we have a valid animation
            if (self.current_animation not in self.sprite_data['animations'] or
                not self.sprite_data['animations'][self.current_animation]):
                self.current_animation = 'idle'
                self.frame_index = 0

            # Double-check idle exists
            if (self.current_animation not in self.sprite_data['animations'] or
                not self.sprite_data['animations'][self.current_animation]):
                return

            frames = self.sprite_data['animations'][self.current_animation]
            if not frames or len(frames) == 0:
                return

            # Ensure frame_index is within bounds before updating
            if self.frame_index >= len(frames) or self.frame_index < 0:
                self.frame_index = 0

            self.animation_timer += dt
            if self.animation_timer >= self.frame_delay:
                self.frame_index = (self.frame_index + 1) % len(frames)
                self.animation_timer = 0

            # Final safety check
            if len(frames) > 0:
                self.frame_index = max(0, min(self.frame_index, len(frames) - 1))

            self.image = self.get_frame()

        except (AttributeError, KeyError, IndexError, TypeError):
            # If any error occurs during animation update, just skip this frame
            pass

class Player(Entity):
    def __init__(self, x, y, sprite_sheet_img):
        super().__init__(x, y, PLAYER_SPRITE_DATA['frame_width'], PLAYER_SPRITE_DATA['frame_height'],
                         sprite_sheet_img, PLAYER_SPRITE_DATA)
        self.speed = 250
        self.health = 100
        self.max_health = 100
        self.score = 0
        self.is_attacking = False
        self.attack_duration = 300
        self.attack_timer = 0
        self.attack_damage = 25
        self.facing_right = True
        self.invulnerable = False
        self.invulnerable_timer = 0
        self.invulnerable_duration = 1000

        # Third-person view properties
        self.angle = 0  # Facing angle in radians (0 = facing right)
        self.pitch = 0  # Vertical look angle (up/down)
        self.mouse_sensitivity = 0.003  # Mouse sensitivity for horizontal look
        self.pitch_sensitivity = PITCH_SENSITIVITY  # Mouse sensitivity for vertical look

        # ODM Gear mechanics
        self.is_grappling = False
        self.grapple_target_x = 0
        self.grapple_target_y = 0
        self.grapple_speed = 400
        self.grapple_range = 300
        self.gas_amount = 100
        self.max_gas = 100
        self.gas_consumption_rate = 20  # per second when grappling

        # Experience and leveling
        self.experience = 0
        self.level = 1
        self.experience_to_next_level = 100

        # Enhanced combat
        self.combo_count = 0
        self.last_attack_time = 0
        self.combo_window = 1000  # ms

    def update(self, dt, keys, mouse_pos, mouse_pressed, mouse_rel=None):
        """Updates player position, animation, and state."""
        global game_running
        if not game_running:
            return
        self.update_animation(dt)

        # Third-person mouse look
        if THIRD_PERSON and mouse_rel:
            # Horizontal look (yaw)
            self.angle += mouse_rel[0] * self.mouse_sensitivity
            # Keep angle in range [0, 2π]
            self.angle = self.angle % (2 * math.pi)

            # Vertical look (pitch)
            self.pitch -= mouse_rel[1] * self.pitch_sensitivity  # Negative for natural mouse look
            # Clamp pitch to prevent over-rotation
            self.pitch = max(MIN_PITCH, min(MAX_PITCH, self.pitch))

            # Update facing_right based on angle
            self.facing_right = -math.pi/2 < self.angle < math.pi/2

        # Gas regeneration when not grappling
        if not self.is_grappling and self.gas_amount < self.max_gas:
            self.gas_amount = min(self.max_gas, self.gas_amount + 30 * dt / 1000)

        # ODM Gear grappling - only to buildings and enemies
        if mouse_pressed[0] and self.gas_amount > 0 and not self.is_grappling:  # Left mouse button
            if THIRD_PERSON:
                # In third-person, grapple only in the direction you're looking to valid targets
                grapple_target = None
                min_distance = float('inf')

                # Cast a ray in the facing direction to find buildings and enemies
                ray_length = self.grapple_range
                ray_step = 20  # Check every 20 units along the ray

                for step in range(0, int(ray_length), ray_step):
                    # Calculate point along the ray
                    ray_x = self.x + self.width/2 + step * math.cos(self.angle)
                    ray_y = self.y + self.height/2 + step * math.sin(self.angle)

                    # Check if this point intersects with any building
                    for building in buildings:
                        if (building.x <= ray_x <= building.x + building.width and
                            building.y <= ray_y <= building.y + building.height):

                            # Found a building in the ray path
                            building_center_x = building.x + building.width/2
                            building_center_y = building.y + building.height/2

                            # Calculate distance
                            dx = building_center_x - (self.x + self.width/2)
                            dy = building_center_y - (self.y + self.height/2)
                            distance = math.sqrt(dx*dx + dy*dy)

                            if distance < min_distance:
                                grapple_target = (building_center_x, building_center_y)
                                min_distance = distance
                            break  # Found a building at this ray step

                    # Also check for titans/enemies in the ray path
                    for titan in titans:
                        titan_center_x = titan.x + titan.width/2
                        titan_center_y = titan.y + titan.height/2

                        # Check if ray point is close to titan
                        titan_distance = math.sqrt((ray_x - titan_center_x)**2 + (ray_y - titan_center_y)**2)
                        if titan_distance <= titan.width/2:  # Within titan's radius
                            # Calculate distance from player to titan
                            dx = titan_center_x - (self.x + self.width/2)
                            dy = titan_center_y - (self.y + self.height/2)
                            distance = math.sqrt(dx*dx + dy*dy)

                            if distance < min_distance:
                                grapple_target = (titan_center_x, titan_center_y)
                                min_distance = distance
                            break  # Found a titan at this ray step

                    if grapple_target:
                        break  # Stop ray casting once we find a target

                if grapple_target:
                    self.is_grappling = True
                    self.grapple_target_x = grapple_target[0]
                    self.grapple_target_y = grapple_target[1]
                    play_sound('grapple_sound')
                    create_grapple_particles(grapple_target[0], grapple_target[1])
            else:
                # Top-down mode: grapple only to buildings and enemies near mouse position
                world_mouse_x = mouse_pos[0] + camera_x
                world_mouse_y = mouse_pos[1] + camera_y
                grapple_target = None
                min_distance = float('inf')

                # Check for buildings near mouse position
                for building in buildings:
                    building_center_x = building.x + building.width/2
                    building_center_y = building.y + building.height/2

                    # Check if mouse is over building
                    if (building.x <= world_mouse_x <= building.x + building.width and
                        building.y <= world_mouse_y <= building.y + building.height):

                        # Calculate distance from player to building
                        dx = building_center_x - (self.x + self.width/2)
                        dy = building_center_y - (self.y + self.height/2)
                        distance = math.sqrt(dx*dx + dy*dy)

                        if distance <= self.grapple_range and distance < min_distance:
                            grapple_target = (building_center_x, building_center_y)
                            min_distance = distance

                # Check for titans near mouse position
                for titan in titans:
                    titan_center_x = titan.x + titan.width/2
                    titan_center_y = titan.y + titan.height/2

                    # Check if mouse is near titan
                    mouse_to_titan_distance = math.sqrt((world_mouse_x - titan_center_x)**2 + (world_mouse_y - titan_center_y)**2)
                    if mouse_to_titan_distance <= titan.width:  # Within titan's area

                        # Calculate distance from player to titan
                        dx = titan_center_x - (self.x + self.width/2)
                        dy = titan_center_y - (self.y + self.height/2)
                        distance = math.sqrt(dx*dx + dy*dy)

                        if distance <= self.grapple_range and distance < min_distance:
                            grapple_target = (titan_center_x, titan_center_y)
                            min_distance = distance

                if grapple_target:
                    self.is_grappling = True
                    self.grapple_target_x = grapple_target[0]
                    self.grapple_target_y = grapple_target[1]
                    play_sound('grapple_sound')
                    create_grapple_particles(grapple_target[0], grapple_target[1])

        # Right click to release grapple
        if mouse_pressed[2] and self.is_grappling:  # Right mouse button
            self.is_grappling = False

        # Handle grappling movement
        if self.is_grappling and self.gas_amount > 0:
            # Consume gas
            self.gas_amount = max(0, self.gas_amount - self.gas_consumption_rate * dt / 1000)

            # Move towards grapple target
            dx = self.grapple_target_x - (self.x + self.width/2)
            dy = self.grapple_target_y - (self.y + self.height/2)
            distance = math.sqrt(dx*dx + dy*dy)

            if distance > 10:  # Still moving towards target
                move_x = (dx / distance) * self.grapple_speed * dt / 1000
                move_y = (dy / distance) * self.grapple_speed * dt / 1000
                self.x += move_x
                self.y += move_y
                self.current_animation = 'attack'  # Use attack animation for grappling
            else:
                self.is_grappling = False
        elif self.gas_amount <= 0:
            self.is_grappling = False

        # Regular movement (only if not grappling)
        if not self.is_grappling:
            if THIRD_PERSON:
                # Third-person movement (WASD relative to camera/facing direction)
                forward = 0
                strafe = 0

                if keys[pygame.K_w] or keys[pygame.K_UP]:
                    forward += 1
                if keys[pygame.K_s] or keys[pygame.K_DOWN]:
                    forward -= 1
                if keys[pygame.K_a] or keys[pygame.K_LEFT]:
                    strafe -= 1
                if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
                    strafe += 1

                # Calculate movement relative to camera angle (not just facing direction)
                if forward != 0 or strafe != 0:
                    # Normalize diagonal movement
                    if forward != 0 and strafe != 0:
                        forward *= 0.707  # 1/sqrt(2)
                        strafe *= 0.707

                    # Use camera angle for movement direction (where camera is looking)
                    camera_angle = self.angle  # Camera faces same direction as player
                    cos_angle = math.cos(camera_angle)
                    sin_angle = math.sin(camera_angle)

                    # Calculate movement in world coordinates
                    move_x = (forward * cos_angle - strafe * sin_angle) * self.speed * dt / 1000
                    move_y = (forward * sin_angle + strafe * cos_angle) * self.speed * dt / 1000

                    self.x += move_x
                    self.y += move_y
            else:
                # Top-down movement (original system)
                move_x, move_y = 0, 0
                if keys[pygame.K_d] or keys[pygame.K_UP]:
                    move_y -= 1
                if keys[pygame.K_s] or keys[pygame.K_DOWN]:
                    move_y += 1
                if keys[pygame.K_a] or keys[pygame.K_LEFT]:
                    move_x -= 1
                    self.facing_right = False
                if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
                    move_x += 1
                    self.facing_right = True

                # Normalize diagonal movement
                if move_x != 0 and move_y != 0:
                    factor = (self.speed * dt / 1000) / (2**0.5)
                    self.x += move_x * factor
                    self.y += move_y * factor
                else:
                    self.x += move_x * self.speed * dt / 1000
                    self.y += move_y * self.speed * dt / 1000

        # Clamp player position within world bounds
        self.x = max(0, min(WORLD_WIDTH - self.width, self.x))
        self.y = max(0, min(WORLD_HEIGHT - self.height, self.y))
        self.rect.topleft = (self.x, self.y)

        # Attack logic
        current_time = pygame.time.get_ticks()
        if self.is_attacking:
            self.attack_timer += dt
            if self.attack_timer >= self.attack_duration:
                self.is_attacking = False
                self.attack_timer = 0
                if not self.is_grappling:
                    self.current_animation = 'idle'
                self.frame_index = 0
        elif keys[pygame.K_SPACE] and not self.is_attacking:
            self.is_attacking = True
            self.attack_timer = 0
            self.current_animation = 'attack'
            self.frame_index = 0

            # Combo system
            if current_time - self.last_attack_time < self.combo_window:
                self.combo_count += 1
            else:
                self.combo_count = 1
            self.last_attack_time = current_time

            play_sound('hit_sound')
        elif not self.is_grappling:
            if keys[pygame.K_w] or keys[pygame.K_s] or keys[pygame.K_a] or keys[pygame.K_d]:
                self.current_animation = 'walk_right' if self.facing_right else 'walk_left'
            else:
                self.current_animation = 'idle'
                self.frame_index = 0

        # Invulnerability
        if self.invulnerable:
            self.invulnerable_timer += dt
            if self.invulnerable_timer >= self.invulnerable_duration:
                self.invulnerable = False
                self.invulnerable_timer = 0

    def take_damage(self, damage):
        # Can't take damage while attacking or already invulnerable
        if not self.invulnerable and not self.is_attacking:
            self.health -= damage
            play_player_hit_sound()
            self.invulnerable = True
            self.invulnerable_timer = 0
            if self.health <= 0:
                self.health = 0
                return True # Player is dead
        return False

class Titan(Entity):
    def __init__(self, x, y, sprite_sheet_img):
        super().__init__(x, y, TITAN_SPRITE_DATA['frame_width'], TITAN_SPRITE_DATA['frame_height'],
                         sprite_sheet_img, TITAN_SPRITE_DATA)
        self.speed = random.randint(50, 100) # Pixels per second
        self.max_health = 100
        self.health = self.max_health
        self.attack_damage = 10
        self.attack_cooldown = 1000 # ms
        self.attack_timer = 0
        self.hit_by_player = False # To ensure only one hit per player attack
        self.facing_right = True # Add facing direction for sprite flipping

    def update(self, dt, player_pos):
        """Updates titan position, animation, and state."""
        global game_running
        if not game_running:
            return
        self.update_animation(dt)

        # Move towards player
        dx = player_pos[0] - self.x
        dy = player_pos[1] - self.y
        dist = (dx**2 + dy**2)**0.5

        if dist > 0: # Avoid division by zero
            # Update facing direction based on movement
            if dx > 0:
                self.facing_right = True
            elif dx < 0:
                self.facing_right = False

            self.x += (dx / dist) * self.speed * dt / 1000
            self.y += (dy / dist) * self.speed * dt / 1000

        self.rect.topleft = (self.x, self.y)

        # Titan Animation (simple walk, or attack if very close)
        if dist < 100: # If close enough to attack
            self.current_animation = 'attack'
        else:
            self.current_animation = 'walk'

        # Attack cooldown
        self.attack_timer += dt

# --- Game Functions ---
def check_collision(rect1, rect2):
    """Checks for simple AABB collision between two pygame Rects."""
    return rect1.colliderect(rect2)

def spawn_titan(player_pos_world):
    """Spawns a titan at a random location far from the player."""
    while True:
        # Spawn outside visible screen area, but within world bounds
        side = random.choice(['top', 'bottom', 'left', 'right'])
        if side == 'top':
            x = random.uniform(0, WORLD_WIDTH - TITAN_SPRITE_DATA['frame_width'] * TITAN_SPRITE_DATA['scale'])
            y = 0 - 200 # Spawn above top edge
        elif side == 'bottom':
            x = random.uniform(0, WORLD_WIDTH - TITAN_SPRITE_DATA['frame_width'] * TITAN_SPRITE_DATA['scale'])
            y = WORLD_HEIGHT + 200 # Spawn below bottom edge
        elif side == 'left':
            x = 0 - 200 # Spawn left of left edge
            y = random.uniform(0, WORLD_HEIGHT - TITAN_SPRITE_DATA['frame_height'] * TITAN_SPRITE_DATA['scale'])
        else: # 'right'
            x = WORLD_WIDTH + 200 # Spawn right of right edge
            y = random.uniform(0, WORLD_HEIGHT - TITAN_SPRITE_DATA['frame_height'] * TITAN_SPRITE_DATA['scale'])

        # Ensure titan is not too close to the player's initial spawn point
        if (abs(x - player_pos_world[0]) > SCREEN_WIDTH / 2 or
            abs(y - player_pos_world[1]) > SCREEN_HEIGHT / 2):
            break

    return Titan(x, y, assets['beast_titan_sprite'])

def play_sound(sound_key):
    """Play a sound effect."""
    if assets.get(sound_key) and assets[sound_key]:
        try:
            assets[sound_key].play()
        except pygame.error:
            pass

def play_attack_sound():
    """Play player attack sound."""
    play_sound('hit_sound')

def play_hit_sound():
    """Play titan hit sound."""
    play_sound('beast_hit_sound')

def play_player_hit_sound():
    """Play player getting hit sound."""
    play_sound('hit_sound')

def play_game_over_sound():
    """Stop all music for game over."""
    stop_music()

def update_game_info(player_obj, wave_num, titans_count):
    """Updates the HUD with player stats and game info."""
    # HUD background
    hud_height = 80
    hud_surface = pygame.Surface((SCREEN_WIDTH, hud_height), pygame.SRCALPHA)
    hud_surface.fill((0, 0, 0, 200))
    screen.blit(hud_surface, (0, 0))

    font_medium = pygame.font.Font(None, 32)
    font_small = pygame.font.Font(None, 24)

    # Health bar
    health_percentage = player_obj.health / player_obj.max_health
    health_bar_width = 200
    health_bar_height = 20
    health_bar_x = 20
    health_bar_y = 15

    # Health bar background
    pygame.draw.rect(screen, DARK_RED, (health_bar_x, health_bar_y, health_bar_width, health_bar_height))
    # Health bar fill
    pygame.draw.rect(screen, RED, (health_bar_x, health_bar_y, health_bar_width * health_percentage, health_bar_height))
    # Health bar border
    pygame.draw.rect(screen, WHITE, (health_bar_x, health_bar_y, health_bar_width, health_bar_height), 2)

    # Health text
    health_text = font_small.render(f"HP: {player_obj.health}/{player_obj.max_health}", True, WHITE)
    screen.blit(health_text, (health_bar_x, health_bar_y + 25))

    # Gas bar (ODM Gear)
    gas_percentage = player_obj.gas_amount / player_obj.max_gas
    gas_bar_x = 20
    gas_bar_y = 50

    # Gas bar background
    pygame.draw.rect(screen, (50, 50, 50), (gas_bar_x, gas_bar_y, health_bar_width, health_bar_height))
    # Gas bar fill
    pygame.draw.rect(screen, BLUE, (gas_bar_x, gas_bar_y, health_bar_width * gas_percentage, health_bar_height))
    # Gas bar border
    pygame.draw.rect(screen, WHITE, (gas_bar_x, gas_bar_y, health_bar_width, health_bar_height), 2)

    # Gas text
    gas_text = font_small.render(f"Gas: {int(player_obj.gas_amount)}/{player_obj.max_gas}", True, WHITE)
    screen.blit(gas_text, (gas_bar_x + health_bar_width + 10, gas_bar_y + 2))

    # Score and stats (top right)
    score_text = font_medium.render(f"Score: {player_obj.score}", True, WHITE)
    wave_text = font_medium.render(f"Wave: {wave_num}", True, YELLOW)
    level_text = font_medium.render(f"Level: {player_obj.level}", True, GREEN)
    titans_text = font_small.render(f"Titans: {titans_count}", True, RED)

    score_rect = score_text.get_rect(topright=(SCREEN_WIDTH - 20, 10))
    wave_rect = wave_text.get_rect(topright=(SCREEN_WIDTH - 20, 35))
    level_rect = level_text.get_rect(topright=(SCREEN_WIDTH - 200, 10))
    titans_rect = titans_text.get_rect(topright=(SCREEN_WIDTH - 200, 35))

    screen.blit(score_text, score_rect)
    screen.blit(wave_text, wave_rect)
    screen.blit(level_text, level_rect)
    screen.blit(titans_text, titans_rect)

    # Combo counter
    if player_obj.combo_count > 1:
        combo_text = font_medium.render(f"COMBO x{player_obj.combo_count}!", True, GOLD)
        combo_rect = combo_text.get_rect(center=(SCREEN_WIDTH/2, 100))
        screen.blit(combo_text, combo_rect)

    # Experience bar (bottom of screen)
    if player_obj.level < 10:  # Max level cap
        exp_bar_width = SCREEN_WIDTH - 40
        exp_bar_height = 10
        exp_bar_x = 20
        exp_bar_y = SCREEN_HEIGHT - 30

        exp_percentage = player_obj.experience / player_obj.experience_to_next_level

        # Experience bar background
        pygame.draw.rect(screen, (50, 50, 50), (exp_bar_x, exp_bar_y, exp_bar_width, exp_bar_height))
        # Experience bar fill
        pygame.draw.rect(screen, GOLD, (exp_bar_x, exp_bar_y, exp_bar_width * exp_percentage, exp_bar_height))
        # Experience bar border
        pygame.draw.rect(screen, WHITE, (exp_bar_x, exp_bar_y, exp_bar_width, exp_bar_height), 1)

        # Experience text
        exp_text = font_small.render(f"EXP: {player_obj.experience}/{player_obj.experience_to_next_level}", True, WHITE)
        screen.blit(exp_text, (exp_bar_x, exp_bar_y - 20))

# --- Menu System ---
def draw_menu():
    """Draw the main menu."""
    screen.fill(BLACK)

    # Background
    loading_image = assets.get('loading_screen')
    if loading_image:
        loading_image = pygame.transform.scale(loading_image, (SCREEN_WIDTH, SCREEN_HEIGHT))
        screen.blit(loading_image, (0, 0))

    # Semi-transparent overlay
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    overlay.set_alpha(128)
    overlay.fill(BLACK)
    screen.blit(overlay, (0, 0))

    # Title
    font_large = pygame.font.Font(None, 100)
    title_text = font_large.render("ATTACK ON TITAN", True, RED)
    subtitle_text = font_large.render("SURVIVE", True, GOLD)

    title_rect = title_text.get_rect(center=(SCREEN_WIDTH/2, 150))
    subtitle_rect = subtitle_text.get_rect(center=(SCREEN_WIDTH/2, 220))

    screen.blit(title_text, title_rect)
    screen.blit(subtitle_text, subtitle_rect)

    # Character selection
    font_medium = pygame.font.Font(None, 50)
    char_text = font_medium.render("Choose Your Character:", True, WHITE)
    char_rect = char_text.get_rect(center=(SCREEN_WIDTH/2, 320))
    screen.blit(char_text, char_rect)

    # Character buttons
    levi_color = GOLD if selected_character == 'levi' else WHITE
    mikasa_color = GOLD if selected_character == 'mikasa' else WHITE

    levi_text = font_medium.render("LEVI", True, levi_color)
    mikasa_text = font_medium.render("MIKASA", True, mikasa_color)

    levi_rect = levi_text.get_rect(center=(SCREEN_WIDTH/2 - 150, 400))
    mikasa_rect = mikasa_text.get_rect(center=(SCREEN_WIDTH/2 + 150, 400))

    # Character selection backgrounds
    if selected_character == 'levi':
        pygame.draw.rect(screen, DARK_RED, levi_rect.inflate(20, 10), border_radius=5)
    if selected_character == 'mikasa':
        pygame.draw.rect(screen, DARK_RED, mikasa_rect.inflate(20, 10), border_radius=5)

    screen.blit(levi_text, levi_rect)
    screen.blit(mikasa_text, mikasa_rect)

    # Start button
    start_text = font_medium.render("PRESS SPACE TO START", True, GREEN)
    start_rect = start_text.get_rect(center=(SCREEN_WIDTH/2, 500))
    screen.blit(start_text, start_rect)

    # Instructions
    font_small = pygame.font.Font(None, 30)
    if THIRD_PERSON:
        instructions = [
            "WASD - Move (W=Forward, S=Back, A/D=Strafe)",
            "Mouse - Look Around",
            "Left Click - Grapple to Buildings & Enemies",
            "Right Click - Release Grapple",
            "Space - Attack (Invulnerable while attacking)",
            "V - Toggle View Mode",
            "ESC - Pause"
        ]
    else:
        instructions = [
            "WASD - Move",
            "Left Click - Grapple to Buildings & Enemies",
            "Right Click - Release Grapple",
            "Space - Attack (Invulnerable while attacking)",
            "V - Toggle View Mode",
            "ESC - Pause"
        ]

    for i, instruction in enumerate(instructions):
        inst_text = font_small.render(instruction, True, WHITE)
        inst_rect = inst_text.get_rect(center=(SCREEN_WIDTH/2, 580 + i * 25))
        screen.blit(inst_text, inst_rect)

    pygame.display.flip()
    return levi_rect, mikasa_rect

def game_over_screen(player_score, wave_num, total_kills_count):
    """Displays the game over screen."""
    play_game_over_sound()
    screen.fill(BLACK)

    # Background
    loading_image = assets.get('loading_screen')
    if loading_image:
        loading_image = pygame.transform.scale(loading_image, (SCREEN_WIDTH, SCREEN_HEIGHT))
        screen.blit(loading_image, (0, 0))

    # Semi-transparent overlay
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    overlay.set_alpha(180)
    overlay.fill(BLACK)
    screen.blit(overlay, (0, 0))

    font_large = pygame.font.Font(None, 100)
    font_medium = pygame.font.Font(None, 50)
    font_small = pygame.font.Font(None, 40)

    game_over_text = font_large.render("HUMANITY FELL", True, RED)

    # Stats
    score_text = font_medium.render(f"Final Score: {player_score}", True, WHITE)
    wave_text = font_medium.render(f"Wave Reached: {wave_num}", True, WHITE)
    kills_text = font_medium.render(f"Titans Slain: {total_kills_count}", True, WHITE)

    # Buttons
    restart_text = font_small.render("R - Restart", True, GREEN)
    menu_text = font_small.render("M - Main Menu", True, YELLOW)
    quit_text = font_small.render("ESC - Quit", True, RED)

    # Positioning
    game_over_rect = game_over_text.get_rect(center=(SCREEN_WIDTH / 2, 200))
    score_rect = score_text.get_rect(center=(SCREEN_WIDTH / 2, 320))
    wave_rect = wave_text.get_rect(center=(SCREEN_WIDTH / 2, 370))
    kills_rect = kills_text.get_rect(center=(SCREEN_WIDTH / 2, 420))

    restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH / 2, 520))
    menu_rect = menu_text.get_rect(center=(SCREEN_WIDTH / 2, 560))
    quit_rect = quit_text.get_rect(center=(SCREEN_WIDTH / 2, 600))

    screen.blit(game_over_text, game_over_rect)
    screen.blit(score_text, score_rect)
    screen.blit(wave_text, wave_rect)
    screen.blit(kills_text, kills_rect)
    screen.blit(restart_text, restart_rect)
    screen.blit(menu_text, menu_rect)
    screen.blit(quit_text, quit_rect)

    pygame.display.flip()

def pause_screen():
    """Displays the pause screen."""
    # Semi-transparent overlay
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    overlay.set_alpha(128)
    overlay.fill(BLACK)
    screen.blit(overlay, (0, 0))

    font_large = pygame.font.Font(None, 100)
    font_medium = pygame.font.Font(None, 50)

    pause_text = font_large.render("PAUSED", True, WHITE)
    resume_text = font_medium.render("ESC - Resume", True, GREEN)
    menu_text = font_medium.render("M - Main Menu", True, YELLOW)

    pause_rect = pause_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 50))
    resume_rect = resume_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 50))
    menu_rect = menu_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 100))

    screen.blit(pause_text, pause_rect)
    screen.blit(resume_text, resume_rect)
    screen.blit(menu_text, menu_rect)

    pygame.display.flip()

def reset_game():
    """Resets all game variables for a new game."""
    global player, titans, titan_spawn_timer, camera_x, camera_y, buildings
    global wave_number, titans_killed_this_wave, titans_needed_for_next_wave, total_kills

    # Choose sprite based on selected character
    sprite_key = 'player_sprite' if selected_character == 'levi' else 'mikasa_sprite'
    player = Player(WORLD_WIDTH / 2, WORLD_HEIGHT / 2, assets[sprite_key])
    titans = []
    titan_spawn_timer = 0
    camera_x, camera_y = 0, 0

    # Generate buildings
    buildings = generate_buildings()

    # Reset wave system
    wave_number = 1
    titans_killed_this_wave = 0
    titans_needed_for_next_wave = 5
    total_kills = 0

    # Spawn initial titans
    for _ in range(3):
        titans.append(spawn_titan((player.x, player.y)))

    # Start game music
    music_tracks = ['game_music_1', 'game_music_2', 'game_music_3']
    selected_track = random.choice(music_tracks)
    if assets.get(selected_track):
        play_music(assets[selected_track])

# Initialize game variables
player = None
titans = []
titan_spawn_timer = 0
buildings = []

def run_game():
    global game_state, camera_x, camera_y, titan_spawn_timer, selected_character
    global wave_number, titans_killed_this_wave, titans_needed_for_next_wave, total_kills

    clock = pygame.time.Clock()

    # Initial loading
    global assets
    assets = load_assets()

    # Start menu music
    if assets.get('menu_music'):
        play_music(assets['menu_music'])

    running = True
    levi_rect = mikasa_rect = None

    while running:
        if game_state == 'MENU':
            dt = clock.tick(MENU_FPS)
        else:
            dt = clock.tick(FPS)

        # Event handling
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                global game_running
                game_running = False
                running = False

            elif event.type == pygame.KEYDOWN:
                if game_state == 'MENU':
                    if event.key == pygame.K_SPACE:
                        game_state = 'PLAYING'
                        reset_game()
                    elif event.key == pygame.K_LEFT or event.key == pygame.K_a:
                        selected_character = 'levi'
                    elif event.key == pygame.K_RIGHT or event.key == pygame.K_d:
                        selected_character = 'mikasa'

                elif game_state == 'PLAYING':
                    if event.key == pygame.K_ESCAPE:
                        game_state = 'PAUSED'
                    elif event.key == pygame.K_v:  # Toggle view mode
                        global THIRD_PERSON
                        THIRD_PERSON = not THIRD_PERSON
                        if not THIRD_PERSON:
                            pygame.mouse.set_visible(True)

                elif game_state == 'PAUSED':
                    if event.key == pygame.K_ESCAPE:
                        game_state = 'PLAYING'
                    elif event.key == pygame.K_m:
                        game_state = 'MENU'
                        if assets.get('menu_music'):
                            play_music(assets['menu_music'])

                elif game_state == 'GAME_OVER':
                    if event.key == pygame.K_r:
                        game_state = 'PLAYING'
                        reset_game()
                    elif event.key == pygame.K_m:
                        game_state = 'MENU'
                        if assets.get('menu_music'):
                            play_music(assets['menu_music'])
                    elif event.key == pygame.K_ESCAPE:
                        running = False

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if game_state == 'MENU' and levi_rect and mikasa_rect:
                    if levi_rect.collidepoint(event.pos):
                        selected_character = 'levi'
                    elif mikasa_rect.collidepoint(event.pos):
                        selected_character = 'mikasa'

        # Game state handling
        if game_state == 'MENU':
            levi_rect, mikasa_rect = draw_menu()

        elif game_state == 'PLAYING':
            keys = pygame.key.get_pressed()
            mouse_pos = pygame.mouse.get_pos()
            mouse_pressed = pygame.mouse.get_pressed()
            mouse_rel = pygame.mouse.get_rel()

            # Capture mouse for third-person view
            if THIRD_PERSON:
                pygame.mouse.set_visible(False)
                # Keep mouse centered for continuous look
                pygame.mouse.set_pos(SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2)

            # --- Update ---
            player.update(dt, keys, mouse_pos, mouse_pressed, mouse_rel)

            # Update Camera based on view mode
            if THIRD_PERSON:
                # Third-person camera follows behind player
                camera_x, camera_y = get_camera_position(
                    player.x + player.width/2,
                    player.y + player.height/2,
                    player.angle
                )
            else:
                # Top-down camera centered on player
                camera_x = player.x - SCREEN_WIDTH / 2
                camera_y = player.y - SCREEN_HEIGHT / 2

                # Clamp camera to world bounds (only for top-down)
                camera_x = max(0, min(WORLD_WIDTH - SCREEN_WIDTH, camera_x))
                camera_y = max(0, min(WORLD_HEIGHT - SCREEN_HEIGHT, camera_y))

            # Wave system - spawn more titans based on wave
            max_titans = min(15, 5 + wave_number * 2)
            titan_spawn_timer += dt
            spawn_interval = max(1000, 3000 - wave_number * 200)  # Faster spawning each wave

            if titan_spawn_timer >= spawn_interval and len(titans) < max_titans:
                titans.append(spawn_titan((player.x, player.y)))
                titan_spawn_timer = 0

            # Update Titans
            for titan in titans:
                titan.update(dt, (player.x, player.y))

            # Update particles
            update_particles(dt)

            # Collision Detection
            # Player Attack vs. Titans
            if player.is_attacking:
                attack_range = 80 + (player.combo_count * 10)  # Larger range with combo
                player_attack_rect = player.rect.inflate(attack_range, attack_range)

                for titan in titans:
                    if check_collision(player_attack_rect, titan.rect):
                        if not titan.hit_by_player:
                            damage = player.attack_damage + (player.combo_count * 5)
                            titan.health -= damage
                            titan.hit_by_player = True
                            play_hit_sound()
                            create_hit_particles(titan.x + titan.width/2, titan.y + titan.height/2)

                            if titan.health <= 0:
                                # Calculate score based on combo and wave
                                base_score = 100
                                combo_bonus = player.combo_count * 25
                                wave_bonus = wave_number * 10
                                total_score = base_score + combo_bonus + wave_bonus

                                player.score += total_score
                                player.experience += 20 + wave_number * 5
                                titans_killed_this_wave += 1
                                total_kills += 1

                                # Level up check
                                if player.experience >= player.experience_to_next_level and player.level < 10:
                                    player.level += 1
                                    player.experience = 0
                                    player.experience_to_next_level += 50
                                    player.max_health += 20
                                    player.health = player.max_health  # Full heal on level up
                                    player.attack_damage += 5
                                    play_sound('jump_sound')  # Level up sound
                    else:
                        titan.hit_by_player = False

            # Titans vs. Player
            for titan in titans:
                if check_collision(player.rect, titan.rect):
                    if titan.attack_timer >= titan.attack_cooldown:
                        damage = titan.attack_damage + (wave_number - 1) * 2  # Stronger titans each wave
                        if player.take_damage(damage):
                            game_state = 'GAME_OVER'
                        titan.attack_timer = 0

            # Remove dead titans
            titans[:] = [titan for titan in titans if titan.health > 0]

            # Wave progression
            if titans_killed_this_wave >= titans_needed_for_next_wave:
                wave_number += 1
                titans_killed_this_wave = 0
                titans_needed_for_next_wave += 3  # More titans needed each wave

                # Bonus for completing wave
                player.score += wave_number * 50
                player.experience += wave_number * 10

                # Heal player slightly
                player.health = min(player.max_health, player.health + 20)

            # --- Render ---
            screen.fill(BLACK)

            if THIRD_PERSON:
                # Use the new 3D rendering system with z-sorting
                render_scene_3d(screen, player, buildings, titans, camera_x, camera_y)
            else:
                # Top-down view rendering
                # Draw static shiganshina background first (no camera movement)
                shiganshina_image = assets.get('shiganshina')
                if shiganshina_image:
                    shiganshina_scaled = pygame.transform.scale(shiganshina_image, (SCREEN_WIDTH, SCREEN_HEIGHT))
                    screen.blit(shiganshina_scaled, (0, 0))

                # Draw other background layers with parallax scrolling (top-down mode)
                bg_layers = ['bg_buildings', 'midground', 'foreground']
                parallax_factors = [0.3, 0.6, 0.8]

                for i, layer_key in enumerate(bg_layers):
                    bg_image = assets.get(layer_key)
                    if bg_image:
                        # Scale background to cover screen
                        bg_image = pygame.transform.scale(bg_image, (SCREEN_WIDTH, SCREEN_HEIGHT))
                        # Apply parallax scrolling
                        bg_x = -(camera_x * parallax_factors[i]) % SCREEN_WIDTH
                        bg_y = -(camera_y * parallax_factors[i]) % SCREEN_HEIGHT

                        # Draw multiple copies to ensure coverage
                        for x_offset in [-SCREEN_WIDTH, 0, SCREEN_WIDTH]:
                            for y_offset in [-SCREEN_HEIGHT, 0, SCREEN_HEIGHT]:
                                screen.blit(bg_image, (bg_x + x_offset, bg_y + y_offset))

                # Draw buildings in top-down mode
                for building in buildings:
                    building_x = building.x - camera_x
                    building_y = building.y - camera_y

                    # Only draw if building is on screen
                    if (building_x + building.width >= 0 and building_x <= SCREEN_WIDTH and
                        building_y + building.height >= 0 and building_y <= SCREEN_HEIGHT):
                        building_sprite = building.get_sprite(1.0)  # No scaling in top-down
                        screen.blit(building_sprite, (building_x, building_y))
                
                # Draw titans in top-down mode
                for titan in titans:
                    # Top-down view (original system)
                    perspective_scale = calculate_perspective_scale(titan.y + titan.height/2)

                    # Get scaled titan image
                    titan_image = titan.get_frame(perspective_scale)

                    # Safety check for facing_right attribute
                    if hasattr(titan, 'facing_right') and not titan.facing_right:
                        titan_image = pygame.transform.flip(titan_image, True, False)

                    # Calculate scaled position (center the scaled sprite)
                    scaled_width = int(titan.width * perspective_scale)
                    scaled_height = int(titan.height * perspective_scale)
                    draw_x = titan.x + (titan.width - scaled_width) // 2 - camera_x
                    draw_y = titan.y + (titan.height - scaled_height) // 2 - camera_y

                    screen.blit(titan_image, (draw_x, draw_y))

                    # Draw health bar above titan with perspective scaling
                    draw_enemy_health_bar(screen, titan, camera_x, camera_y, perspective_scale)
                
                # Draw player in top-down mode
                player_perspective_scale = calculate_perspective_scale(player.y + player.height/2)
                player_image = player.get_frame(player_perspective_scale)

                if hasattr(player, 'facing_right') and not player.facing_right:
                    player_image = pygame.transform.flip(player_image, True, False)

                # Apply visual effects
                draw_player = True
                if player.invulnerable and (player.invulnerable_timer // 100) % 2:
                    # Create a flashing effect by skipping drawing every other frame
                    draw_player = False

                if draw_player:
                    # Calculate scaled position (center the scaled sprite)
                    scaled_width = int(player.width * player_perspective_scale)
                    scaled_height = int(player.height * player_perspective_scale)
                    player_draw_x = player.x + (player.width - scaled_width) // 2 - camera_x
                    player_draw_y = player.y + (player.height - scaled_height) // 2 - camera_y

                    # Add a glow effect when attacking (invulnerable during attack)
                    if player.is_attacking:
                        # Create a glowing outline effect scaled with perspective
                        glow_padding = int(8 * player_perspective_scale)
                        glow_surface = pygame.Surface((scaled_width + glow_padding, scaled_height + glow_padding), pygame.SRCALPHA)
                        glow_color = (*GOLD[:3], 100)  # Semi-transparent gold
                        pygame.draw.rect(glow_surface, glow_color, (0, 0, scaled_width + glow_padding, scaled_height + glow_padding), border_radius=5)
                        screen.blit(glow_surface, (player_draw_x - glow_padding//2, player_draw_y - glow_padding//2))

                    screen.blit(player_image, (player_draw_x, player_draw_y))
                
                # Draw grappling hook line in top-down mode
                if player.is_grappling:
                    # Calculate player center with perspective scaling
                    player_perspective_scale = calculate_perspective_scale(player.y + player.height/2)
                    scaled_width = int(player.width * player_perspective_scale)
                    scaled_height = int(player.height * player_perspective_scale)
                    player_center_x = player.x + (player.width - scaled_width) // 2 + scaled_width//2 - camera_x
                    player_center_y = player.y + (player.height - scaled_height) // 2 + scaled_height//2 - camera_y

                    start_pos = (player_center_x, player_center_y)
                    end_pos = (player.grapple_target_x - camera_x, player.grapple_target_y - camera_y)

                    # Scale line thickness with perspective
                    line_thickness = max(1, int(3 * player_perspective_scale))
                    pygame.draw.line(screen, BLUE, start_pos, end_pos, line_thickness)

                    # Draw grapple target indicator (scaled)
                    target_scale = calculate_perspective_scale(player.grapple_target_y)
                    target_radius = max(3, int(8 * target_scale))
                    target_thickness = max(1, int(2 * target_scale))
                    pygame.draw.circle(screen, YELLOW, (int(end_pos[0]), int(end_pos[1])), target_radius, target_thickness)


            # Draw UI
            update_game_info(player, wave_number, len(titans))

            pygame.display.flip()

        elif game_state == 'PAUSED':
            pause_screen()

        elif game_state == 'GAME_OVER':
            game_over_screen(player.score, wave_number, total_kills)

    pygame.quit()
    sys.exit(0)

if __name__ == "__main__":
    run_game()
